parameters:
- name: environment
  type: string
- name: azureDevopsEnvironment
  type: string
- name: serviceConnection
  type: string
- name: dependsOn
  type: object
  default: []

stages:
- stage: deploy_${{ parameters.environment }}
  displayName: Deploy to ${{ upper(parameters.environment) }}
  dependsOn: ${{ parameters.dependsOn }}
  variables:
  - group: ${{ parameters.environment }}VariableGroupTridentPCP
  - name: RunNumber
    value: ${{ parameters.environment }}-$(Build.BuildNumber)
  - name: AppName
    value: pcp
  - name: LocAbbrev
    ${{ if eq(parameters.environment, 'dev') }}:
      value: eus2
    ${{ else }}:
      value: weu
  - name: CompanyPrefix
    ${{ if eq(parameters.environment, 'dev') }}:
      value: npdev
    ${{ else }}:
      value: ttg
  - name: DataFactoryName
    value: adf-$(AppName)-${{ parameters.environment }}-$(LocAbbrev)
  - name: ResourceGroupName
    ${{ if eq(parameters.environment, 'dev') }}:
      value: rg-$(CompanyPrefix)-$(AppName)-app-$(LocAbbrev)
    ${{ else }}:
      value: rg-$(CompanyPrefix)-$(AppName)-${{ parameters.environment }}-app-$(LocAbbrev)
  jobs:
  - deployment: deploy_${{ parameters.environment }}
    displayName: Deploy to ${{ upper(parameters.environment) }}
    environment: ${{ parameters.azureDevopsEnvironment }}
    strategy:
      runOnce:
        deploy:
          steps:
          - checkout: self

          - task: AzureCLI@2
            displayName: Pre-deployment - Stop ADF Triggers
            name: pre_deployment_adf_triggers
            inputs:
              azureSubscription: ${{ parameters.ServiceConnection }}
              scriptType: pscore
              scriptLocation: scriptPath
              scriptPath: scripts/Manage-AdfTriggers.ps1
              arguments: >
                -Action "stop"
                -DataFactoryName "$(DataFactoryName)"
                -ResourceGroupName "$(ResourceGroupName)"
                -SubscriptionId "$(varPCPSubId)"

          - task: AzureCLI@2
            displayName: Deploy All Infrastructure
            name: deploy_infrastructure
            inputs:
              azureSubscription: ${{ parameters.ServiceConnection }}
              scriptType: pscore
              scriptLocation: inlineScript
              inlineScript: |
                az account set --subscription $(varPCPSubId)
                az deployment sub create `
                --template-file main.bicep `
                --parameters main.${{ parameters.environment }}.bicepparam `
                --parameters parApiClientSecretAcc=$(varApiClientSecretAcc) `
                --parameters parApiClientSecretPrd=$(varApiClientSecretPrd) `
                --parameters parApiSmtpPasswordAcc=$(varApiSmtpPasswordAcc) `
                --parameters parApiSmtpPasswordPrd=$(varApiSmtpPasswordPrd) `
                --parameters parCltClientSecretAcc=$(varCltClientSecretAcc) `
                --parameters parCltClientSecretExternalIdAcc=$(varCltClientSecretExternalIdAcc) `
                --parameters parCltClientSecretExternalIdPrd=$(varCltClientSecretExternalIdPrd) `
                --parameters parCltClientSecretPrd=$(varCltClientSecretPrd) `
                --parameters parMgtClientSecretAcc=$(varMgtClientSecretAcc) `
                --parameters parMgtClientSecretPrd=$(varMgtClientSecretPrd) `
                --parameters parMgtSessionSecretAcc=$(varMgtSessionSecretAcc) `
                --parameters parMgtSessionSecretPrd=$(varMgtSessionSecretPrd) `
                --location $(varLocation) `
                --name deploy_infrastructure-$(RunNumber)

          - task: AzureCLI@2
            displayName: Post-deployment - Start ADF Triggers
            name: post_deployment_adf_triggers
            condition: always()
            inputs:
              azureSubscription: ${{ parameters.ServiceConnection }}
              scriptType: pscore
              scriptLocation: scriptPath
              scriptPath: scripts/Manage-AdfTriggers.ps1
              arguments: >
                -Action "start"
                -DataFactoryName "$(DataFactoryName)"
                -ResourceGroupName "$(ResourceGroupName)"
                -SubscriptionId "$(varPCPSubId)"
                -EnabledTriggersList "$(pre_deployment_adf_triggers.EnabledTriggers)"
              
