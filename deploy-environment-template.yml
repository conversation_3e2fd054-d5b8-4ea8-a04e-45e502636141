parameters:
- name: environment
  type: string
- name: azureDevopsEnvironment
  type: string
- name: serviceConnection
  type: string
- name: dependsOn
  type: object
  default: []

stages:
- stage: deploy_${{ parameters.environment }}
  displayName: Deploy to ${{ upper(parameters.environment) }}
  dependsOn: ${{ parameters.dependsOn }}
  variables:
  - group: ${{ parameters.environment }}VariableGroupTridentPCP
  - name: RunNumber
    value: ${{ parameters.environment }}-$(Build.BuildNumber)
  - name: AppName
    value: pcp
  - name: LocAbbrev
    ${{ if eq(parameters.environment, 'dev') }}:
      value: eus2
    ${{ else }}:
      value: weu
  - name: CompanyPrefix
    ${{ if eq(parameters.environment, 'dev') }}:
      value: npdev
    ${{ else }}:
      value: ttg
  - name: DataFactoryName
    value: adf-$(AppName)-${{ parameters.environment }}-$(LocAbbrev)
  - name: ResourceGroupName
    ${{ if eq(parameters.environment, 'dev') }}:
      value: rg-$(CompanyPrefix)-$(AppName)-app-$(LocAbbrev)
    ${{ else }}:
      value: rg-$(CompanyPrefix)-$(AppName)-${{ parameters.environment }}-app-$(LocAbbrev)
  jobs:
  - deployment: deploy_${{ parameters.environment }}
    displayName: Deploy to ${{ upper(parameters.environment) }}
    environment: ${{ parameters.azureDevopsEnvironment }}
    strategy:
      runOnce:
        deploy:
          steps:
          - checkout: self

          - task: AzureCLI@2
            displayName: Pre-deployment - Manage ADF Triggers
            name: pre_deployment_adf_triggers
            inputs:
              azureSubscription: ${{ parameters.ServiceConnection }}
              scriptType: pscore
              scriptLocation: inlineScript
              inlineScript: |
                az account set --subscription $(varPCPSubId)

                # Initialize variables
                $dataFactoryName = "$(DataFactoryName)"
                $resourceGroupName = "$(ResourceGroupName)"
                $enabledTriggers = @()

                Write-Host "Checking if Data Factory '$dataFactoryName' exists in resource group '$resourceGroupName'..."

                try {
                  # Check if Data Factory exists
                  $adfExists = az datafactory show --name $dataFactoryName --resource-group $resourceGroupName 2>$null

                  if ($adfExists) {
                    Write-Host "Data Factory found. Checking for active triggers..."

                    # Get all triggers
                    $triggersJson = az datafactory trigger list --factory-name $dataFactoryName --resource-group $resourceGroupName --output json

                    if ($triggersJson) {
                      $triggers = $triggersJson | ConvertFrom-Json

                      foreach ($trigger in $triggers) {
                        Write-Host "Checking trigger: $($trigger.name)"

                        # Get trigger details to check if it's enabled
                        $triggerDetailsJson = az datafactory trigger show --factory-name $dataFactoryName --resource-group $resourceGroupName --name $trigger.name --output json
                        $triggerDetails = $triggerDetailsJson | ConvertFrom-Json

                        if ($triggerDetails.properties.runtimeState -eq "Started") {
                          Write-Host "Trigger '$($trigger.name)' is enabled. Adding to list for later restoration."
                          $enabledTriggers += $trigger.name

                          # Stop the trigger
                          Write-Host "Stopping trigger '$($trigger.name)'..."
                          az datafactory trigger stop --factory-name $dataFactoryName --resource-group $resourceGroupName --name $trigger.name

                          if ($LASTEXITCODE -eq 0) {
                            Write-Host "Successfully stopped trigger '$($trigger.name)'"
                          } else {
                            Write-Warning "Failed to stop trigger '$($trigger.name)'"
                          }
                        } else {
                          Write-Host "Trigger '$($trigger.name)' is already stopped."
                        }
                      }

                      # Store enabled triggers list for post-deployment
                      $enabledTriggersJson = $enabledTriggers | ConvertTo-Json -Compress
                      Write-Host "##vso[task.setvariable variable=EnabledTriggers;isOutput=true]$enabledTriggersJson"
                      Write-Host "Stored enabled triggers for restoration: $enabledTriggersJson"
                    } else {
                      Write-Host "No triggers found in Data Factory."
                      Write-Host "##vso[task.setvariable variable=EnabledTriggers;isOutput=true][]"
                    }
                  } else {
                    Write-Host "Data Factory does not exist yet. This might be an initial deployment."
                    Write-Host "##vso[task.setvariable variable=EnabledTriggers;isOutput=true][]"
                  }
                } catch {
                  Write-Warning "Error occurred while managing triggers: $($_.Exception.Message)"
                  Write-Host "##vso[task.setvariable variable=EnabledTriggers;isOutput=true][]"
                }

          - task: AzureCLI@2
            displayName: Deploy All Infrastructure
            name: deploy_infrastructure
            inputs:
              azureSubscription: ${{ parameters.ServiceConnection }}
              scriptType: pscore
              scriptLocation: inlineScript
              inlineScript: |
                az account set --subscription $(varPCPSubId)
                az deployment sub create `
                --template-file main.bicep `
                --parameters main.${{ parameters.environment }}.bicepparam `
                --parameters parApiClientSecretAcc=$(varApiClientSecretAcc) `
                --parameters parApiClientSecretPrd=$(varApiClientSecretPrd) `
                --parameters parApiSmtpPasswordAcc=$(varApiSmtpPasswordAcc) `
                --parameters parApiSmtpPasswordPrd=$(varApiSmtpPasswordPrd) `
                --parameters parCltClientSecretAcc=$(varCltClientSecretAcc) `
                --parameters parCltClientSecretExternalIdAcc=$(varCltClientSecretExternalIdAcc) `
                --parameters parCltClientSecretExternalIdPrd=$(varCltClientSecretExternalIdPrd) `
                --parameters parCltClientSecretPrd=$(varCltClientSecretPrd) `
                --parameters parMgtClientSecretAcc=$(varMgtClientSecretAcc) `
                --parameters parMgtClientSecretPrd=$(varMgtClientSecretPrd) `
                --parameters parMgtSessionSecretAcc=$(varMgtSessionSecretAcc) `
                --parameters parMgtSessionSecretPrd=$(varMgtSessionSecretPrd) `
                --location $(varLocation) `
                --name deploy_infrastructure-$(RunNumber)

          - task: AzureCLI@2
            displayName: Post-deployment - Restore ADF Triggers
            name: post_deployment_adf_triggers
            condition: always()
            inputs:
              azureSubscription: ${{ parameters.ServiceConnection }}
              scriptType: pscore
              scriptLocation: inlineScript
              inlineScript: |
                az account set --subscription $(varPCPSubId)

                # Get the list of enabled triggers from pre-deployment step
                $enabledTriggersJson = "$(pre_deployment_adf_triggers.EnabledTriggers)"
                $dataFactoryName = "$(DataFactoryName)"
                $resourceGroupName = "$(ResourceGroupName)"

                Write-Host "Restoring ADF triggers..."
                Write-Host "Enabled triggers to restore: $enabledTriggersJson"

                if ($enabledTriggersJson -and $enabledTriggersJson -ne "[]" -and $enabledTriggersJson -ne "") {
                  try {
                    $enabledTriggers = $enabledTriggersJson | ConvertFrom-Json

                    if ($enabledTriggers -is [array] -and $enabledTriggers.Count -gt 0) {
                      foreach ($triggerName in $enabledTriggers) {
                        Write-Host "Re-enabling trigger '$triggerName'..."

                        try {
                          az datafactory trigger start --factory-name $dataFactoryName --resource-group $resourceGroupName --name $triggerName

                          if ($LASTEXITCODE -eq 0) {
                            Write-Host "Successfully re-enabled trigger '$triggerName'"
                          } else {
                            Write-Warning "Failed to re-enable trigger '$triggerName'"
                          }
                        } catch {
                          Write-Warning "Error re-enabling trigger '$triggerName': $($_.Exception.Message)"
                        }
                      }
                    } elseif ($enabledTriggers -is [string]) {
                      # Handle case where there's only one trigger (not an array)
                      Write-Host "Re-enabling single trigger '$enabledTriggers'..."

                      try {
                        az datafactory trigger start --factory-name $dataFactoryName --resource-group $resourceGroupName --name $enabledTriggers

                        if ($LASTEXITCODE -eq 0) {
                          Write-Host "Successfully re-enabled trigger '$enabledTriggers'"
                        } else {
                          Write-Warning "Failed to re-enable trigger '$enabledTriggers'"
                        }
                      } catch {
                        Write-Warning "Error re-enabling trigger '$enabledTriggers': $($_.Exception.Message)"
                      }
                    } else {
                      Write-Host "No triggers to restore."
                    }
                  } catch {
                    Write-Warning "Error parsing enabled triggers list: $($_.Exception.Message)"
                  }
                } else {
                  Write-Host "No enabled triggers to restore."
                }
              
