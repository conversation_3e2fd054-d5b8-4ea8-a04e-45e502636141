parameters:
- name: environment
  type: string
- name: azureDevopsEnvironment
  type: string
- name: serviceConnection
  type: string
- name: dependsOn
  type: object
  default: []

stages:
- stage: deploy_${{ parameters.environment }}
  displayName: Deploy to ${{ upper(parameters.environment) }}
  dependsOn: ${{ parameters.dependsOn }}
  variables:
  - group: ${{ parameters.environment }}VariableGroupTridentPCP
  - name: RunNumber
    value: ${{ parameters.environment }}-$(Build.BuildNumber)
  jobs:
  - deployment: deploy_${{ parameters.environment }}
    displayName: Deploy to ${{ upper(parameters.environment) }}
    environment: ${{ parameters.azureDevopsEnvironment }}
    strategy:
      runOnce:
        deploy:
          steps:
          - task: AzureCLI@2
            displayName: Deploy All Infrastructure
            name: deploy_infrastructure
            inputs:
              azureSubscription: ${{ parameters.ServiceConnection }}
              scriptType: pscore
              scriptLocation: inlineScript
              inlineScript: |
                az account set --subscription $(varPCPSubId)
                az deployment sub create `
                --template-file main.bicep `
                --parameters main.${{ parameters.environment }}.bicepparam `
                --parameters parApiClientSecretAcc=$(varApiClientSecretAcc) `
                --parameters parApiClientSecretPrd=$(varApiClientSecretPrd) `
                --parameters parApiSmtpPasswordAcc=$(varApiSmtpPasswordAcc) `
                --parameters parApiSmtpPasswordPrd=$(varApiSmtpPasswordPrd) `
                --parameters parCltClientSecretAcc=$(varCltClientSecretAcc) `
                --parameters parCltClientSecretExternalIdAcc=$(varCltClientSecretExternalIdAcc) `
                --parameters parCltClientSecretExternalIdPrd=$(varCltClientSecretExternalIdPrd) `
                --parameters parCltClientSecretPrd=$(varCltClientSecretPrd) `
                --parameters parMgtClientSecretAcc=$(varMgtClientSecretAcc) `
                --parameters parMgtClientSecretPrd=$(varMgtClientSecretPrd) `
                --parameters parMgtSessionSecretAcc=$(varMgtSessionSecretAcc) `
                --parameters parMgtSessionSecretPrd=$(varMgtSessionSecretPrd) `
                --location $(varLocation) `
                --name deploy_infrastructure-$(RunNumber)
              
